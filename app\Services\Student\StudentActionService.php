<?php

namespace App\Services\Student;

use App\Actions\CreateTag;
use App\Enums\OptionType;
use App\Models\GroupMember;
use App\Models\Option;
use App\Models\Student\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class StudentActionService
{
    public function setDefaultPeriod(Request $request, Student $student)
    {
        $contact = $student->contact;

        $user = $contact->user;

        if (! $user) {
            throw ValidationException::withMessages([
                'message' => trans('global.could_not_find', ['attribute' => trans('user.user')]),
            ]);
        }

        $preference = $user->user_preference;
        $preference['academic'] = Arr::get($preference, 'academic', []);
        $preference['academic']['period_id'] = $student->period_id;

        $user->preference = $preference;
        $user->save();
    }

    public function updateTags(Request $request, Student $student)
    {
        $request->validate([
            'tags' => 'array',
            'tags.*' => 'required|string|distinct',
        ]);

        $tags = (new CreateTag)->execute($request->input('tags', []));

        $student->tags()->sync($tags);
    }

    private function getBulkStudents(Request $request): array
    {
        $selectAll = $request->boolean('select_all');

        $students = [];

        if ($selectAll) {
            $students = (new StudentListService)->listAll($request)->toArray();
        } else {
            $students = Student::query()
                ->whereIn('uuid', $request->students)
                ->filterAccessible(false)
                ->get()
                ->toArray();

            if (array_diff($request->students, Arr::pluck($students, 'uuid'))) {
                throw ValidationException::withMessages(['message' => trans('general.errors.invalid_input')]);
            }
        }

        $uniqueStudents = array_unique(Arr::pluck($students, 'uuid'));

        if (count($uniqueStudents) !== count($students)) {
            throw ValidationException::withMessages(['message' => trans('general.errors.invalid_input')]);
        }

        return $students;
    }

    public function updateBulkTags(Request $request)
    {
        $request->validate([
            'action' => 'required|string|in:assign,remove',
            'students' => 'array',
            'tags' => 'array',
            'tags.*' => 'required|string|distinct',
        ]);

        $students = $this->getBulkStudents($request);

        $tags = (new CreateTag)->execute($request->input('tags', []));

        if ($request->input('action') === 'assign') {
            foreach ($students as $student) {
                Student::query()
                    ->whereUuid(Arr::get($student, 'uuid'))
                    ->first()
                    ->tags()
                    ->sync($tags);
            }
        } else {
            foreach ($students as $student) {
                Student::query()
                    ->whereUuid(Arr::get($student, 'uuid'))
                    ->first()
                    ->tags()
                    ->detach($tags);
            }
        }
    }

    public function updateBulkGroups(Request $request)
    {
        $request->validate([
            'action' => 'required|string|in:assign,remove',
            'students' => 'array',
            'groups' => 'array',
            'groups.*' => 'required|uuid|distinct',
        ]);

        $studentGroup = Option::query()
            ->byTeam()
            ->where('type', OptionType::STUDENT_GROUP)
            ->whereIn('uuid', $request->input('groups', []))
            ->get();

        if (! $studentGroup->count()) {
            throw ValidationException::withMessages(['message' => trans('general.errors.invalid_input')]);
        }

        $students = $this->getBulkStudents($request);

        if ($request->input('action') === 'assign') {
            foreach ($students as $student) {
                foreach ($studentGroup as $group) {
                    GroupMember::firstOrCreate([
                        'model_type' => 'Student',
                        'model_id' => Arr::get($student, 'id'),
                        'model_group_id' => $group->id,
                    ]);
                }
            }
        } else {
            foreach ($students as $student) {
                GroupMember::where('model_type', 'Student')
                    ->where('model_id', Arr::get($student, 'id'))
                    ->whereIn('model_group_id', $studentGroup->pluck('id'))
                    ->delete();
            }
        }
    }

    public function massCreateAccounts(Request $request): array
    {
        $request->validate([
            'students' => 'array',
            'role' => 'required|string|in:student,guardian',
        ]);

        $students = $this->getBulkStudents($request);
        $role = $request->input('role', 'student');

        $createdCount = 0;
        $skippedCount = 0;
        $totalCount = count($students);
        $errors = [];

        \DB::beginTransaction();

        foreach ($students as $studentData) {
            try {
                $student = Student::query()
                    ->with(['contact', 'guardians.contact'])
                    ->whereUuid(Arr::get($studentData, 'uuid'))
                    ->first();

                if (!$student) {
                    $skippedCount++;
                    $errors[] = "Student not found: " . Arr::get($studentData, 'uuid');
                    continue;
                }

                if ($role === 'student') {
                    $result = $this->createStudentAccount($student);
                } else {
                    $result = $this->createGuardianAccounts($student);
                }

                if ($result['created']) {
                    $createdCount += $result['count'];
                } else {
                    $skippedCount++;
                    if ($result['error']) {
                        $errors[] = $result['error'];
                    }
                }
            } catch (\Exception $e) {
                $skippedCount++;
                $errors[] = "Error creating account for student " . Arr::get($studentData, 'name', 'Unknown') . ": " . $e->getMessage();
            }
        }

        \DB::commit();

        return [
            'created_count' => $createdCount,
            'skipped_count' => $skippedCount,
            'total_count' => $totalCount,
            'errors' => $errors,
        ];
    }

    private function createStudentAccount(Student $student): array
    {
        $contact = $student->contact;

        if ($contact->user_id) {
            return ['created' => false, 'count' => 0, 'error' => "Student {$contact->name} already has a user account"];
        }

        if (!$contact->first_name || !$contact->last_name) {
            return ['created' => false, 'count' => 0, 'error' => "Student {$contact->name} is missing first name or last name"];
        }

        $user = \App\Models\User::forceCreate([
            'name' => $contact->name,
            'username' => $contact->first_name,
            'password' => bcrypt($contact->last_name),
            'status' => \App\Enums\UserStatus::ACTIVATED,
        ]);

        $user->assignRole('student');

        $contact->user_id = $user->id;
        $contact->save();

        return ['created' => true, 'count' => 1, 'error' => null];
    }

    private function createGuardianAccounts(Student $student): array
    {
        $guardians = $student->guardians;
        $createdCount = 0;
        $errors = [];

        foreach ($guardians as $guardian) {
            $contact = $guardian->contact;

            if ($contact->user_id) {
                $errors[] = "Guardian {$contact->name} already has a user account";
                continue;
            }

            if (!$contact->first_name || !$contact->last_name) {
                $errors[] = "Guardian {$contact->name} is missing first name or last name";
                continue;
            }

            $user = \App\Models\User::forceCreate([
                'name' => $contact->name,
                'username' => $contact->first_name,
                'password' => bcrypt($contact->last_name),
                'status' => \App\Enums\UserStatus::ACTIVATED,
            ]);

            $user->assignRole('guardian');

            $contact->user_id = $user->id;
            $contact->save();

            $createdCount++;
        }

        return [
            'created' => $createdCount > 0,
            'count' => $createdCount,
            'error' => !empty($errors) ? implode('; ', $errors) : null
        ];
    }
}
