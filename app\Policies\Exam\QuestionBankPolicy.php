<?php

namespace App\Policies\Exam;

use App\Models\Exam\QuestionBank;
use App\Models\Employee\Employee;
use App\Models\Incharge;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class QuestionBankPolicy
{
    use HandlesAuthorization;

    private function validatePeriod(User $user, QuestionBank $questionBank)
    {
        return $questionBank->subject->period_id == $user->current_period_id;
    }

    private function validateTeacherAccess(User $user, QuestionBank $questionBank)
    {
        // Admin, student, and guardian have full access
        if ($user->hasAnyRole(['admin', 'student', 'guardian'])) {
            return true;
        }

        // For teachers, check if they created the question or teach the subject
        $employee = Employee::query()->auth()->first();

        if (!$employee) {
            return false;
        }

        // Check if user created this question
        if ($questionBank->user_id === $user->id) {
            return true;
        }

        // Check if teacher is incharge of the subject
        $subjectIncharges = Incharge::query()
            ->whereEmployeeId($employee->id)
            ->whereModelType('SubjectRecord')
            ->where('start_date', '<=', today()->toDateString())
            ->where(function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', today()->toDateString());
            })
            ->pluck('model_id')
            ->all();

        if (!empty($subjectIncharges)) {
            $subjectIds = \DB::table('subject_records')
                ->whereIn('id', $subjectIncharges)
                ->pluck('subject_id')
                ->all();

            return in_array($questionBank->subject_id, $subjectIds);
        }

        return false;
    }

    private function validateTeacherEditAccess(User $user, QuestionBank $questionBank)
    {
        // Admin has full access
        if ($user->hasRole('admin')) {
            return true;
        }

        // For teachers, they can only edit questions they created
        return $questionBank->user_id === $user->id;
    }

    /**
     * Determine whether the user can fetch prerequisites any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function preRequisite(User $user)
    {
        return $user->can('question-bank:create') || $user->can('question-bank:edit');
    }

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('question-bank:read');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, QuestionBank $questionBank)
    {
        if (! $this->validatePeriod($user, $questionBank)) {
            return false;
        }

        if (! $this->validateTeacherAccess($user, $questionBank)) {
            return false;
        }

        return $user->can('question-bank:read');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('question-bank:create');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, QuestionBank $questionBank)
    {
        if (! $this->validatePeriod($user, $questionBank)) {
            return false;
        }

        if (! $this->validateTeacherEditAccess($user, $questionBank)) {
            return false;
        }

        return $user->can('question-bank:edit');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, QuestionBank $questionBank)
    {
        if (! $this->validatePeriod($user, $questionBank)) {
            return false;
        }

        if (! $this->validateTeacherEditAccess($user, $questionBank)) {
            return false;
        }

        return $user->can('question-bank:delete');
    }
}
