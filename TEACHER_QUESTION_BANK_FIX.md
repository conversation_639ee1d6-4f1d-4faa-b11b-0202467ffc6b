# Teacher Question Bank Access Fix

## Issue
Teachers were seeing all subjects in the question bank form instead of only the subjects they are assigned to teach (subject incharge).

## Root Cause
The `filterTeacherSubjects` scope was using the wrong model type (`SubjectRecord` instead of `Subject`) for the incharge relationship lookup.

## Solution
Updated the subject filtering logic to use the correct incharge model relationship:

### Changes Made

1. **Fixed Subject Model (`app/Models/Academic/Subject.php`)**
   - Updated `filterTeacherSubjects()` scope to use `whereModelType('Subject')` instead of `whereModelType('SubjectRecord')`
   - Simplified the logic to directly use subject IDs from incharge records
   - Made the filtering always apply for security (not dependent on config setting)

2. **Fixed QuestionBank Model (`app/Models/Exam/QuestionBank.php`)**
   - Updated `filterAccessible()` scope to use the same correct incharge lookup
   - Teachers can now see questions for subjects they teach OR questions they created themselves

3. **Added Database Migration**
   - Created new migration: `2025_01_23_000002_add_user_id_to_question_banks_table.php`
   - Adds `user_id` field to track who created each question

## How It Works Now

### For Teachers:
- Can only see subjects they are assigned as incharge of in the question bank form
- Can view question banks for:
  - Subjects they are incharge of (regardless of who created the questions)
  - Questions they created themselves (regardless of subject)
- Can only edit/delete questions they created themselves

### For Admins:
- Can see all subjects and all question banks (no restrictions)

### For Students/Guardians:
- No restrictions applied (existing behavior maintained)

## Testing Instructions

### Manual Testing:

1. **Setup Test Data:**
   ```sql
   -- Create a teacher user and assign role
   -- Create subjects (Math, Science, English)
   -- Assign teacher as incharge of only Math and Science
   ```

2. **Test Subject Filtering:**
   - Login as teacher
   - Go to Question Bank > Add Question
   - Verify only Math and Science appear in subject dropdown
   - Verify English does not appear

3. **Test Question Bank Viewing:**
   - Create questions in Math (by teacher)
   - Create questions in Science (by admin)
   - Create questions in English (by admin)
   - Login as teacher
   - Verify teacher can see:
     - Math questions (because incharge)
     - Science questions created by teacher
   - Verify teacher cannot see:
     - English questions (not incharge and didn't create)

4. **Test Admin Access:**
   - Login as admin
   - Verify admin can see all subjects and all questions

### Automated Testing:
Run the test file: `tests/Feature/Exam/QuestionBankTeacherAccessTest.php`

## Database Migration
Run the migration to add the user_id field:
```bash
php artisan migrate
```

## Files Modified
- `app/Models/Academic/Subject.php`
- `app/Models/Exam/QuestionBank.php`
- `app/Services/Exam/QuestionBankService.php`
- `app/Policies/Exam/QuestionBankPolicy.php`
- `database/migrations/2025_01_23_000002_add_user_id_to_question_banks_table.php`

## Security Considerations
- Teachers can only create questions for subjects they teach
- Teachers can only edit/delete questions they created
- Question viewing is restricted based on subject incharge assignments
- Admin access remains unrestricted for management purposes

## Notes
- The fix uses the existing incharge system correctly
- No configuration dependencies (always applies for security)
- Backward compatible with existing question banks (user_id will be null for existing records)
- Follows existing codebase patterns and conventions
