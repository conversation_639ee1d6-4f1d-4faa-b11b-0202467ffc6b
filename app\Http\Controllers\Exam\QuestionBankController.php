<?php

namespace App\Http\Controllers\Exam;

use App\Http\Controllers\Controller;
use App\Http\Requests\Exam\QuestionBankRequest;
use App\Http\Resources\Exam\QuestionBankResource;
use App\Models\Exam\QuestionBank;
use App\Services\Exam\QuestionBankListService;
use App\Services\Exam\QuestionBankService;
use Illuminate\Http\Request;

class QuestionBankController extends Controller
{
    public function __construct()
    {
        $this->middleware('test.mode.restriction')->only(['destroy']);
    }

    public function preRequisite(Request $request, QuestionBankService $service)
    {
        $this->authorize('preRequisite', QuestionBank::class);

        return response()->ok($service->preRequisite($request));
    }

    public function preRequisiteForFilter(Request $request, QuestionBankService $service)
    {
        $this->authorize('preRequisite', QuestionBank::class);

        return response()->ok($service->preRequisiteForFilter($request));
    }

    public function index(Request $request, QuestionBankListService $service)
    {
        $this->authorize('viewAny', QuestionBank::class);

        return $service->paginate($request);
    }

    public function store(QuestionBankRequest $request, QuestionBankService $service)
    {
        $this->authorize('create', QuestionBank::class);

        $questionBank = $service->create($request);

        return response()->success([
            'message' => trans('global.created', ['attribute' => trans('exam.question_bank.question_bank')]),
            'question_bank' => QuestionBankResource::make($questionBank),
        ]);
    }

    public function show(Request $request, string $questionBank, QuestionBankService $service)
    {
        $questionBank = $service->findByUuidOrFail($questionBank);

        $this->authorize('view', $questionBank);

        return QuestionBankResource::make($questionBank);
    }

    public function update(QuestionBankRequest $request, string $questionBank, QuestionBankService $service)
    {
        $questionBank = $service->findByUuidOrFail($questionBank);

        $this->authorize('update', $questionBank);

        $service->update($request, $questionBank);

        return response()->success([
            'message' => trans('global.updated', ['attribute' => trans('exam.question_bank.question_bank')]),
        ]);
    }

    public function destroy(Request $request, string $questionBank, QuestionBankService $service)
    {
        $questionBank = $service->findByUuidOrFail($questionBank);

        $this->authorize('delete', $questionBank);

        $service->delete($questionBank);

        return response()->success([
            'message' => trans('global.deleted', ['attribute' => trans('exam.question_bank.question_bank')]),
        ]);
    }

    public function parseQuestions(Request $request, QuestionBankService $service)
    {
        $this->authorize('create', QuestionBank::class);

        $result = $service->parseQuestions($request);

        return response()->success($result);
    }

    public function bulkCreate(Request $request, QuestionBankService $service)
    {
        $this->authorize('create', QuestionBank::class);

        $result = $service->bulkCreate($request);

        return response()->success([
            'message' => trans('exam.question_bank.bulk_create_success', [
                'count' => $result['created_count'],
                'total' => $result['total_questions']
            ]),
            'result' => $result,
        ]);
    }

    /**
     * Generate HMAC-SHA256 signature for request body
     *
     * @param mixed $requestBody The request body to sign
     * @param string $key The secret key for HMAC generation
     * @return string Base64 encoded HMAC-SHA256 hash
     */
    private function generateSignature($requestBody, string $key): string
    {
        // Convert request body to JSON string
        $jsonString = json_encode($requestBody);

        // Generate HMAC-SHA256 hash
        $hash = hash_hmac('sha256', $jsonString, $key, true);

        // Convert hash to Base64 string
        return base64_encode($hash);
    }
}
