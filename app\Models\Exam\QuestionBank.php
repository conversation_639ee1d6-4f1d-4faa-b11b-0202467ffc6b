<?php

namespace App\Models\Exam;

use App\Concerns\HasConfig;
use App\Concerns\HasFilter;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use App\Enums\Exam\OnlineExamQuestionType;
use App\Models\Academic\Batch;
use App\Models\Academic\Subject;
use App\Models\Employee\Employee;
use App\Models\Incharge;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class QuestionBank extends Model
{
    use HasConfig, HasFactory, HasFilter, HasMeta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $primaryKey = 'id';

    protected $table = 'question_banks';

    protected $casts = [
        'type' => OnlineExamQuestionType::class,
        'options' => 'array',
        'answer_config' => 'array',
        'config' => 'array',
        'meta' => 'array',
    ];

    public function getModelName(): string
    {
        return 'QuestionBank';
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function batches(): BelongsToMany
    {
        return $this->belongsToMany(Batch::class, 'question_bank_batches')
            ->withPivot('uuid', 'config', 'meta', 'created_at', 'updated_at')
            ->withTimestamps()
            ->with('course');
    }

    public function scopeBySubject(Builder $query, $subjectId = null)
    {
        if ($subjectId) {
            $query->where('subject_id', $subjectId);
        }
    }

    public function scopeByClass(Builder $query, $class = null)
    {
        if ($class) {
            $query->where('class', $class);
        }
    }

    public function scopeByType(Builder $query, $type = null)
    {
        if ($type) {
            $query->where('type', $type);
        }
    }

    public function scopeByBatch(Builder $query, $batchId = null)
    {
        if ($batchId) {
            $query->whereHas('batches', function ($q) use ($batchId) {
                $q->where('batch_id', $batchId);
            });
        }
    }

    public function scopeFilterAccessible(Builder $query)
    {
        // Filter by current period's subjects
        $query->whereHas('subject', function ($q) {
            $q->byPeriod();
        });

        // If user is not admin, student, or guardian, apply teacher-specific filtering
        if (!auth()->user()->hasAnyRole(['admin', 'student', 'guardian'])) {
            $employee = Employee::query()
                ->auth()
                ->first();

            if ($employee) {
                // Get subjects that this teacher is incharge of
                $subjectIncharges = Incharge::query()
                    ->whereEmployeeId($employee->id)
                    ->whereModelType('SubjectRecord')
                    ->where('start_date', '<=', today()->toDateString())
                    ->where(function ($q) {
                        $q->whereNull('end_date')
                            ->orWhere('end_date', '>=', today()->toDateString());
                    })
                    ->pluck('model_id')
                    ->all();

                if (!empty($subjectIncharges)) {
                    // Get subject IDs from subject records
                    $subjectIds = \DB::table('subject_records')
                        ->whereIn('id', $subjectIncharges)
                        ->pluck('subject_id')
                        ->all();

                    $query->where(function ($q) use ($subjectIds) {
                        // Can see questions for subjects they teach
                        $q->whereIn('subject_id', $subjectIds)
                            // OR questions they created themselves
                            ->orWhere('user_id', auth()->id());
                    });
                } else {
                    // If teacher has no subject assignments, only show questions they created
                    $query->where('user_id', auth()->id());
                }
            } else {
                // If not an employee, only show questions they created
                $query->where('user_id', auth()->id());
            }
        }
    }

    public function scopeFilterTeacherSubjects(Builder $query)
    {
        // If user is not admin, student, or guardian, filter subjects they can create questions for
        if (!auth()->user()->hasAnyRole(['admin', 'student', 'guardian'])) {
            $employee = Employee::query()
                ->auth()
                ->first();

            if ($employee) {
                // Get subjects that this teacher is incharge of
                $subjectIncharges = Incharge::query()
                    ->whereEmployeeId($employee->id)
                    ->whereModelType('SubjectRecord')
                    ->where('start_date', '<=', today()->toDateString())
                    ->where(function ($q) {
                        $q->whereNull('end_date')
                            ->orWhere('end_date', '>=', today()->toDateString());
                    })
                    ->pluck('model_id')
                    ->all();

                if (!empty($subjectIncharges)) {
                    // Get subject IDs from subject records
                    $subjectIds = \DB::table('subject_records')
                        ->whereIn('id', $subjectIncharges)
                        ->pluck('subject_id')
                        ->all();

                    $query->whereIn('id', $subjectIds);
                } else {
                    // If teacher has no subject assignments, they can't create questions
                    $query->whereRaw('1 = 0'); // No results
                }
            } else {
                // If not an employee, they can't create questions
                $query->whereRaw('1 = 0'); // No results
            }
        }
    }

    public function scopeFindByUuidOrFail(Builder $query, string $uuid)
    {
        return $query
            ->filterAccessible()
            ->where('question_banks.uuid', $uuid)
            ->getOrFail(trans('exam.question_bank.question_bank'));
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('question_bank')
            ->logAll()
            ->logExcept(['updated_at'])
            ->logOnlyDirty();
    }
}
