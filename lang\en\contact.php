<?php

return [
    'contact' => 'Contact',
    'contacts' => 'Contacts',
    'module_title' => 'List all Contacts',
    'module_description' => 'Manage all Contacts',
    'info' => 'Contact Information',
    'props' => [
        'name' => 'Name',
        'first_name' => 'First Name',
        'middle_name' => 'Middle Name',
        'third_name' => 'Third Name',
        'last_name' => 'Last Name',
        'father_name' => 'Father Name',
        'mother_name' => 'Mother Name',
        'birth_date' => 'Birth Date',
        'anniversary_date' => 'Anniversary Date',
        'contact_number' => 'Contact Number',
        'primary_contact_number' => 'Primary Contact Number',
        'alternate_contact_number' => 'Alternate Contact Number',
        'father_contact_number' => 'Father Contact Number',
        'mother_contact_number' => 'Mother Contact Number',
        'father_email' => 'Father Email',
        'mother_email' => 'Mother Email',
        'gender' => 'Gender',
        'email' => 'Email',
        'alternate_email' => 'Alternate Email',
        'photo' => 'Photo',
        'occupation' => 'Occupation',
        'annual_income' => 'Annual Income',
        'birth_place' => 'Birth Place',
        'nationality' => 'Nationality',
        'mother_tongue' => 'Mother Tongue',
        'blood_group' => 'Blood Group',
        'relation' => 'Relation',
        'marital_status' => 'Marital Status',
        'address_display' => 'Address',
        'present_address' => 'Present Address',
        'permanent_address' => 'Permanent Address',
        'same_as_present_address' => 'Same as Present Address',
        'source' => 'Source',
        'address' => [
            'address' => 'Address',
            'address_line1' => 'Address Line 1',
            'address_line2' => 'Address Line 2',
            'city' => 'City',
            'state' => 'State',
            'zipcode' => 'Zipcode',
            'country' => 'Country',
        ],
        'id_proof' => 'ID Proof',
        'address_proof' => 'Address Proof',
        'signature' => 'Signature',
    ],
    'sources' => [
        'visitor' => 'Visitor',
        'guardian' => 'Guardian',
        'employee' => 'Employee',
        'enquiry' => 'Enquiry',
        'job_applicant' => 'Job Applicant',
        'student' => 'Student',
        'online_registration' => 'Online Registration',
        'candidate' => 'Candidate',
    ],
    'config' => [
        'config' => 'Config',
        'general' => 'General',
        'props' => [
            'enable_middle_name_field' => 'Enable Middle Name Field',
            'enable_third_name_field' => 'Enable Third Name Field',
            'unique_id_number1_label' => 'Unique ID 1 Label',
            'unique_id_number2_label' => 'Unique ID 2 Label',
            'unique_id_number3_label' => 'Unique ID 3 Label',
            'unique_id_number1_required' => 'Unique ID 1 Required',
            'unique_id_number2_required' => 'Unique ID 2 Required',
            'unique_id_number3_required' => 'Unique ID 3 Required',
        ],
    ],
    'caste' => [
        'caste' => 'Caste',
        'castes' => 'Castes',
        'module_title' => 'List all Castes',
        'module_description' => 'Manage all Castes',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
        ],
    ],
    'category' => [
        'category' => 'Category',
        'categories' => 'Categories',
        'module_title' => 'List all Categories',
        'module_description' => 'Manage all Categories',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
        ],
    ],
    'religion' => [
        'religion' => 'Religion',
        'religions' => 'Religions',
        'module_title' => 'List all Religions',
        'module_description' => 'Manage all Religions',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
        ],
    ],
    'login' => [
        'login' => 'User Login',
        'no_login_found' => 'User login details not found.',
        'email_belongs_to_other_contact' => 'This email belongs to other contact.',
        'email_belongs_to_team_member' => 'This email belongs to team member\'s email.',
        'already_has_account' => 'This contact already has a user account.',
        'missing_name_fields' => 'First name and last name are required to create user account.',
        'props' => [
            'username' => 'Username',
            'email' => 'Email',
            'role' => 'Role',
            'password' => 'Password',
            'password_confirmation' => 'Confirm Password',
        ],
    ],
    'user_account' => 'User Account',
    'action' => 'action',
    'comment' => 'comment',
    'edit_request' => [
        'statuses' => [
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
        ],
    ],
    'verification' => [
        'props' => [
            'comment' => 'Comment',
            'action' => 'Action',
        ],
        'statuses' => [
            'pending' => 'Not Verified',
            'verified' => 'Verified',
            'rejected' => 'Rejected',
        ],
        'action' => [
            'verify' => 'Verify',
            'reject' => 'Reject',
        ],
    ],
];
