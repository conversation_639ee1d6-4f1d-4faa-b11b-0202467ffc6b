<?php

return [
    'student' => 'Student',
    'students' => 'Students',
    'module_title' => 'List all Students',
    'module_description' => 'Manage all Students',
    'set_fee_info' => 'Fee is not set for this student.',
    'could_not_delete_with_multiple_records' => 'Could not delete student if already promoted.',
    'could_not_delete_with_paid_fee' => 'Could not delete student if fee is paid.',
    'sibling' => [
        'sibling' => 'Sibling',
        'module_title' => 'List all Siblings',
        'module_description' => 'Manage all Siblings',
        'report' => 'Sibling Report',
    ],
    'statuses' => [
        'cancelled' => 'Cancelled',
        'studying' => 'Studying',
        'transferred' => 'Transferred',
    ],
    'props' => [
        'status' => 'Status',
        'name' => 'Student Name',
        'parent' => 'Parent',
        'roll_number' => 'Roll Number',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
    ],
    'config' => [
        'config' => 'Config',
        'info' => 'Student Config',
        'props' => [
            'number_prefix' => 'Admission Number Prefix',
            'number_suffix' => 'Admission Number Suffix',
            'number_digit' => 'Admission Number Digit',
            'attendance_past_day_limit' => 'No of days to mark attendance in past',
            'allow_student_to_submit_contact_edit_request' => 'Allow student to submit contact edit request',
            'late_fee_waiver_till_date' => 'Late Fee waiver till date',
            'allow_flexible_installment_payment' => 'Allow flexible installment payment',
            'allow_flexible_installment_payment_info' => 'Allow making payment of any installment for fee regardles of installment sequence',
        ],
    ],
    'record' => [
        'record' => 'Record',
        'module_title' => 'List all Student Records',
        'module_description' => 'Manage all Student Records',
        'could_not_change_batch_with_different_fee_allocation' => 'Could not change class arm with different fee allocation.',
        'could_not_cancel_previous_admission' => 'Could not cancel admission if promoted.',
        'could_not_cancel_previous_promotion' => 'Could not cancel promotion if promoted to next Academic Session.',
        'could_not_cancel_if_paid' => 'Could not cancel record if fee is paid.',
        'code_number_same_as_previous_code_number' => 'The admission number is same as previous admission number.',
        'code_number_format_mismatch' => 'The admission number format does not match.',
        'code_number_already_exists' => 'The admission number already exists.',
        'props' => [
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'promotion_date' => 'Promotion Date',
            'remarks' => 'Remarks',
        ],
    ],
    'audience_types' => [
        'all' => 'All Student',
        'division_wise' => 'Division Wise Student',
        'course_wise' => 'Student (Class)',
        'batch_wise' => 'Student (Class Arm)',
    ],
    'custom_fee' => [
        'custom_fee' => 'Custom Fee',
        'module_title' => 'List all Custom Fee',
        'module_description' => 'Manage all Custom Fee',
    ],
    'fee' => [
        'fee' => 'Fee',
        'total' => 'Total Fee',
        'paid' => 'Paid Fee',
        'balance' => 'Balance Fee',
        'opted_fee' => 'Opted Fee',
        'receipt' => 'Fee Receipt',
        'authorized_signatory' => 'Authorized Signatory',
        'custom_fee' => 'Custom Fee',
        'head_wise' => 'Fee Head Wise',
        'group_wise' => 'Fee Group Wise',
        'set_fee_info' => 'Fee is not set for this student.',
        'no_allocation_found' => 'No fee allocation found to remove.',
        'could_not_remove_paid_allocation' => 'Could not remove fee allocation as fee is paid.',
        'multi_installment_payment_info' => 'The payment is made for multiple installments.',
        'paid_online' => 'Fee payment of :amount successful. Reference number :reference.',
        'head_amount_mismatch' => 'Amount mismatch for :attribute.',
        'total_head_amount_mismatch' => 'Input amount :input does not match with total payable amount :total.',
        'props' => [
            'amount' => 'Amount',
            'date' => 'Date',
            'due_date' => 'Due Date',
            'additional_charge' => 'Additional Charge',
            'additional_charge_label' => 'Label',
            'additional_charge_amount' => 'Amount',
            'additional_discount' => 'Additional Discount',
            'additional_discount_label' => 'Label',
            'additional_discount_amount' => 'Amount',
            'remarks' => 'Remarks',
        ],
        'could_not_pay_lt_zero' => 'Installment amount cannot be less than zero.',
        'could_not_change_payment_date' => 'Could not change payment date.',
        'could_not_customize_late_fee' => 'Could not change late fee.',
        'could_not_apply_late_fee_without_installment' => 'Could not apply late fee without installment.',
        'could_not_apply_additional_fee_without_installment' => 'Could not apply additional fee without installment.',
        'partial_payment_not_allowed_for_custom_late_fee' => 'Partial payment is not allowed for custom late fee.',
        'partial_payment_not_allowed_for_late_fee' => 'Partial payment is not allowed for late fee.',
        'partial_payment_not_allowed_for_additional_fee' => 'Partial payment is not allowed for additional fee.',
        'previous_installment_due' => 'Could not make payment of this installment as previous installment is due.',
        'no_payable_balance' => 'Could not find correct balance amount for payment. Please use :attribute for future reference.',
        'no_payable_fee' => 'Could not find any payable fee installment.',
        'could_not_make_partial_payment' => 'Could not make partial payment.',
        'could_not_make_excess_payment' => 'Amount cannot be greater than balance :attribute.',
        'could_not_set_if_fee_already_set' => 'Fee is already set.',
        'could_not_reset_if_fee_paid' => 'Could not reset fee as fee is paid.',
        'could_not_edit_if_fee_paid' => 'Could not edit fee as fee is paid.',
        'could_not_reset_if_fee_not_set' => 'Fee is not yet set.',
        'could_not_cancel_online_payment' => 'Could not cancel online payment.',
        'already_cancelled' => 'Payment is already cancelled.',
        'previous_due_info' => 'You have due amount of :amount + applicable late fee for :period.',
    ],
    'fee_refund' => [
        'fee_refund' => 'Fee Refund',
        'fee_refunds' => 'Fee Refunds',
        'module_title' => 'List all Fee Refund',
        'module_description' => 'Manage all Fee Refund',
        'props' => [
            'amount' => 'Amount',
            'total' => 'Total',
            'date' => 'Date',
            'remarks' => 'Remarks',
        ],
    ],
    'payment' => [
        'payment' => 'Payment',
        'online' => 'Online Payment',
        'instruction' => 'Payment Instruction',
        'payment_instruction_alert' => 'Please read the instruction carefully before proceeding.',
        'already_processed' => 'This payment is already processed.',
        'payment_completed' => 'Payment completed successfully.',
        'link_expired' => 'Payment link expired.',
        'pay' => 'Pay',
        'props' => [
            'remarks' => 'Remarks',
        ],
    ],
    'admission' => [
        'admission' => 'Admission',
        'admissions' => 'Admissions',
        'module_title' => 'List all Admissions',
        'module_description' => 'Manage all Admissions',
        'date_lt_registration_date' => 'Admission date cannot be less than registration date :attribute.',
        'props' => [
            'number' => 'Number',
            'code_number' => 'Admission Number',
            'code_number_format' => 'Admission Number Format',
            'code_number_sno' => 'Admission Number S.No.',
            'date' => 'Date of Admission',
            'remarks' => 'Admission Remarks',
        ],
    ],
    'transfer_request' => [
        'transfer_request' => 'Transfer Request',
        'transfer_requests' => 'Transfer Requests',
        'already_transferred' => 'Student is already transferred.',
        'module_title' => 'List all Student Transfer Requests',
        'module_description' => 'Manage all Student Transfer Requests',
        'date_before_joining' => 'Transfer request date cannot be less than joining date :attribute.',
        'date_before_promotion' => 'Transfer request date cannot be less than promotion date :attribute.',
        'duplicate_request' => 'You have already submitted a transfer request.',
        'could_not_perform_if_status_updated' => 'You cannot perform this action as status is updated.',
        'fee_due' => 'You have paid :paid of :total. Please pay the remaining amount to proceed.',
        'props' => [
            'action' => 'Action',
            'code_number' => 'Request #',
            'reason' => 'Reason',
            'status' => 'Status',
            'application' => 'Application',
            'request_date' => 'Date of Request',
            'processed_at' => 'Processed At',
            'comment' => 'Comment',
            'certificate_number' => 'Certificate Number',
        ],
        'statuses' => [
            'requested' => 'Requested',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'in_progress' => 'In Progress',
        ],
        'config' => [
            'props' => [
                'number_prefix' => 'Transfer Request Number Prefix',
                'number_suffix' => 'Transfer Request Number Suffix',
                'number_digit' => 'Transfer Request Number Digit',
            ],
        ],
    ],
    'transfer' => [
        'transfer' => 'Transfer',
        'transfers' => 'Transfers',
        'module_title' => 'List all Student Transfers',
        'module_description' => 'Manage all Student Transfers',
        'already_transferred' => 'Student is already transferred.',
        'date_before_joining' => 'Transfer date cannot be less than joining date :attribute.',
        'date_before_promotion' => 'Transfer date cannot be less than promotion date :attribute.',
        'already_promoted' => 'Student is already promoted to next class.',
        'could_not_perform_if_transferred' => 'You cannot perform this action as student is transferred.',
        'could_not_perform_if_transfer_request' => 'Could not perform this action if processed via transfer request.',
        'props' => [
            'code_number' => 'Transfer Number',
            'date' => 'Date of Transfer',
            'reason' => 'Reason',
            'remarks' => 'Transfer Remarks',
            'certificate_number' => 'Certificate Number',
        ],
        'config' => [
            'props' => [
                'number_prefix' => 'Transfer Number Prefix',
                'number_suffix' => 'Transfer Number Suffix',
                'number_digit' => 'Transfer Number Digit',
            ],
        ],
    ],
    'registration' => [
        'registration' => 'Registration',
        'registrations' => 'Registrations',
        'fee' => 'Registration Fee',
        'receipt' => 'Fee Receipt',
        'online' => 'Online',
        'offline' => 'Offline',
        'module_title' => 'List all Registrations',
        'module_description' => 'Manage all Registrations',
        'action' => 'Action',
        'proceed_without_payment' => 'Proceed without payment',
        'payment_date_lt_date' => 'Payment date cannot be less than registration date :attribute.',
        'could_not_delete_if_not_pending' => 'Only pending status registration can be deleted.',
        'could_not_edit_if_not_pending' => 'Only pending status registration can be edited.',
        'could_not_delete_transaction_if_processed' => 'Only unprocessed registration related transaction can be deleted.',
        'could_not_edit_if_paid' => 'Only unpaid registration can be edited',
        'could_not_delete_if_paid' => 'Only unpaid registration can be deleted.',
        'could_not_find_elective_subjects' => 'Could not find elective subjects :attribute for this class arm.',
        'fee_unpaid' => 'Could not proceed as registration fee is not paid.',
        'rejected_info' => 'Your registration is rejected at :attribute.',
        'approved_info' => 'Your registration is approved.',
        'initiated_info' => 'This application is not yet completed.',
        'online_registration_instruction_alert' => 'Please read the instruction carefully before proceeding.',
        'config' => [
            'config' => 'Config',
            'info' => 'Registration Config',
            'props' => [
                'number_prefix' => 'Registration Number Prefix',
                'number_suffix' => 'Registration Number Suffix',
                'number_digit' => 'Registration Number Digit',
            ],
        ],
        'statuses' => [
            'initiated' => 'Initiated',
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
        ],
        'actions' => [
            'approve' => 'Approve',
            'reject' => 'Reject',
        ],
        'props' => [
            'status' => 'Status',
            'type' => 'Type',
            'date' => 'Date of Registration',
            'code_number' => 'Registration Number',
            'payment_status' => 'Payment Status',
            'payment_date' => 'Date of Payment',
            'payment_remarks' => 'Remarks',
            'rejection_remarks' => 'Remarks',
        ],
    ],
    'online_registration' => [
        'title' => 'Online Registration',
        'subtitle' => 'Register Online for Admission',
        'completed' => 'Your registration is completed. Your application number is :attribute, keep it safe for future reference.',
        'enable_registration' => 'Enable Registration',
        'invalid_otp' => 'The code entered is invalid.',
        'application' => 'Application',
        'application_number' => 'Application Number',
        'proceed_with_payment' => 'Proceed with Payment',
        'fee_already_paid' => 'Fee is already paid for this registration.',
        'invalid_amount' => 'Invalid amount for registration fee.',
        'pending_application_exists' => 'You have already submitted an application.',
        'no_batches_available' => 'No class arm available for this class.',
        'received' => 'Your application is received and email needs confirmation. Please check your email :email for one time passcode.',
        'confirmed' => 'Your email is confirmed and application number is :attribute.',
        'submitted' => 'Your application is submitted and under review.',
        'already_have_application_number_info' => 'Already have application number? Click here.',
        'initiated_info' => 'This application is not yet completed.',
        'pending_info' => 'Your application is under review.',
        'rejected_info' => 'Your registration is rejected at :attribute.',
        'approved_info' => 'Your registration is approved.',
        'declaration' => 'All the information provided is true and accurate.',
        'basic_info_required' => 'Please complete basic information before updating contact details',
        'contact_info_required' => 'Please complete contact information before uploading file.',
        'upload_file_required' => 'Please upload file before submitting application.',
        'wizard' => [
            'first_step' => 'Basic Info',
            'second_step' => 'Contact Info',
            'third_step' => 'File Upload',
            'final_step' => 'Declaration',
            'transfer_certificate' => 'Transfer Certificate',
            'marksheet' => 'Previous Sheet Card',
            'declaration' => 'Declaration',
            'review' => 'Review',
            'review_content' => 'Please review the information provided below.',
            'declaration_content' => 'I hereby declare that the information provided is true and accurate.',
        ],
        'props' => [
            'number' => 'Application Number',
            'confirm' => 'Confirm',
            'code' => 'Code',
            'verify' => 'Verify',
            'date_of_submission' => 'Date of Submission',
            'place_of_submission' => 'Place of Submission',
        ],
    ],
    'roll_number' => [
        'roll_number' => 'Roll Number',
        'roll_numbers' => 'Roll Numbers',
        'auto_assign' => 'Auto Assign',
        'props' => [
        ],
    ],
    'health_record' => [
        'health_record' => 'Health Record',
        'health_records' => 'Health Records',
        'props' => [
            'date' => 'Date',
            'general' => 'General',
            'height' => 'Height',
            'weight' => 'Weight',
            'chest' => 'Chest',
            'vision' => 'Vision',
            'left_eye' => 'Left Eye',
            'right_eye' => 'Right Eye',
            'dental' => 'Dental',
            'dental_hygiene' => 'Dental Hygiene',
        ],
    ],
    'subject' => [
        'subject' => 'Elective Subject',
        'props' => [
        ],
    ],
    'attendance' => [
        'attendance' => 'Attendance',
        'attendances' => 'Attendances',
        'student_attendance' => 'Student Attendance',
        'module_title' => 'List all Attendances',
        'module_description' => 'Manage all Attendances',
        'attendance_chart' => 'Attendance Chart',
        'absentees' => 'Absentees',
        'method' => 'Method',
        'subject_wise' => 'Subject Wise',
        'batch_wise' => 'Class Arm Wise',
        'session' => 'Session',
        'is_default' => 'Default',
        'could_not_mark_if_holiday' => 'Attendance cannot be marked as it is a holiday.',
        'could_not_mark_past_date' => 'Attendance cannot be marked past :attribute days.',
        'could_not_mark_in_future' => 'Attendance cannot be marked in future.',
        'holiday_force_attendance_marked' => 'Date is marked as holiday (:attribute) but attendance is marked forcefully.',
        'attendance_force_holiday_marked' => 'Date is forcefully marked as holiday.',
        'marked' => 'Attendance marked.',
        'removed' => 'Attendance removed.',
        'qr_code_expired' => 'QR Code expired.',
        'already_marked' => 'Attendance already marked.',
        'session' => 'Session',
        'props' => [
            'type' => 'Attendance Type',
            'date' => 'Date of Attendance',
        ],
        'status' => 'status',
        'statuses' => [
            'marked' => 'Marked',
            'not_marked' => 'Not Marked',
        ],
        'timesheet' => [
            'timesheet' => 'Timesheet',
            'timesheets' => 'Timesheets',
            'recently_marked' => 'You have recently marked attendance.',
            'already_clocked_in' => 'You are already clocked in.',
            'not_clocked_in' => 'You are not clocked in.',
            'minimum_diff_between_clock_in_out' => 'Minimum difference between clock in and clock out time should be :attribute minutes.',
            'start_time_should_less_than_end_time' => 'Start time should less than end time.',
            'module_title' => 'Manage all Student Timesheets',
            'module_description' => 'Timesheet is the record of student\'s attendance for a particular day.',
            'geolocation_not_supported' => 'Sorry! Your browser doesn\'t support Geolocation.',
            'unable_to_detect_geolocation' => 'Unable to detect your current location.',
            'could_not_mark_attendance_outside_geolocation' => 'You are :distance mtr away. Could not mark attendance outside geolocation.',
            'statuses' => [
                'ok' => 'OK',
                'manual_attendance' => 'Manual Attendance',
            ],
            'props' => [
                'manual' => 'Manual',
                'clock_in' => 'Clock In',
                'clock_out' => 'Clock Out',
                'in_at' => 'In at',
                'out_at' => 'Out at',
                'date' => 'Date',
                'duration' => 'Duration',
                'remarks' => 'Remarks',
            ],
        ],
        'mark' => 'Mark',
        'mark_as_holiday' => 'Mark as Holiday',
        'holiday_reason' => 'Reason',
        'mark_all' => 'Mark all :attribute',
        'total_working_days' => 'Total Working Days',
        'types' => [
            'working_days' => 'Working Days',
            'present' => 'Present',
            'absent' => 'Absent',
            'leave' => 'Leave',
            'holiday' => 'Holiday',
            'late' => 'Late',
            'half_day' => 'Half Day',
        ],
    ],
    'fee_allocation' => [
        'fee_allocation' => 'Fee Allocation',
        'fee_allocations' => 'Fee Allocations',
        'module_title' => 'List Student Fee Allocations',
        'module_description' => 'Manage Student Fee Allocations',
    ],
    'promotion' => [
        'promotion' => 'Promotion',
        'promote' => 'Promote',
        'awaited_promotion' => 'Student awaited Promotion',
        'promoted' => 'Student promoted.',
        'period_date_before_current_period' => 'Next Academic Session date cannot be less than current academic session date.',
        'date_before_current_period' => 'Promotion date cannot be less than academic session date.',
        'duplicate_student_found' => 'Duplicate student found.',
        'props' => [
            'date' => 'Promotion Date',
        ],
    ],
    'alumni' => [
        'alumni' => 'Alumni',
        'marked_as_alumni' => 'Student marked as alumni.',
        'period' => 'Alumni Academic Session',
        'props' => [
            'date' => 'Alumni Date',
        ],
    ],
    'login' => [
        'login' => 'User Login',
    ],
    'account' => [
        'account' => 'Account',
        'accounts' => 'Accounts',
        'module_title' => 'Manage all Account Records',
        'module_description' => 'Keep all account related information of your students.',
        'auto_creation_success' => 'User account created successfully! Username: :username, Password: :password',
        'mass_creation_success' => 'Mass account creation completed! Created: :created, Total: :total, Skipped: :skipped',
    ],
    'transfer_reason' => [
        'transfer_reason' => 'Transfer Reason',
        'transfer_reasons' => 'Transfer Reasons',
        'module_title' => 'List all Transfer Reasons',
        'module_description' => 'Manage all Transfer Reasons',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
        ],
    ],
    'enrollment_type' => [
        'enrollment_type' => 'Enrollment Type',
        'enrollment_types' => 'Enrollment Types',
        'module_title' => 'List all Enrollment Types',
        'module_description' => 'Manage all Enrollment Types',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
        ],
    ],
    'group' => [
        'group' => 'Student Group',
        'groups' => 'Student Groups',
        'module_title' => 'List all Student Groups',
        'module_description' => 'Manage all Student Groups',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
        ],
    ],
    'document_type' => [
        'document_type' => 'Document Type',
        'document_types' => 'Document Types',
        'module_title' => 'List all Document Types',
        'module_description' => 'Manage all Document Types',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
        ],
    ],
    'document' => [
        'document' => 'Document',
        'module_title' => 'Manage all Student Documents',
        'module_description' => 'Categorize the documents related to your students.',
        'props' => [
            'title' => 'Title',
            'start_date' => 'Validity Start',
            'end_date' => 'Validity End',
            'description' => 'Description',
        ],
    ],
    'qualification_level' => [
        'qualification_level' => 'Qualification Level',
        'module_title' => 'Manage all Qualification Levels',
        'module_description' => 'Qualification levels are the standards of different programs the institutes can offer.',
        'module_example' => 'Graduate, Post Graduate, Doctorate are some examples of Qualification Levels.',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
        ],
    ],
    'qualification' => [
        'qualification' => 'Qualification',
        'module_title' => 'Manage all Student Qualification Records',
        'module_description' => 'Keep the documents related to your student\'s qualification.',
        'props' => [
            'course' => 'Class',
            'institute' => 'School',
            'type' => 'Type',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'affiliated_to' => 'Affiliated To',
            'result' => 'Result',
        ],
    ],
    'leave_category' => [
        'leave_category' => 'Leave Category',
        'leave_categories' => 'Leave Categories',
        'module_title' => 'List all Leave Categories',
        'module_description' => 'Manage all Leave Categories',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
        ],
    ],
    'leave_request' => [
        'leave_request' => 'Leave Request',
        'leave_requests' => 'Leave Requests',
        'student_leave_request' => 'Student Leave Request',
        'module_title' => 'List all Leave Requests',
        'module_description' => 'Manage all Leave Requests',
        'date_before_joining' => 'Leave date cannot be less than joining date :attribute.',
        'date_before_promotion' => 'Leave date cannot be less than promotion date :attribute.',
        'range_exists' => 'Leave request for the student already exists between :start and :end.',
        'could_not_perform_if_status_updated' => 'Could not perform this operation if status is already updated.',
        'could_not_perform_for_past_date' => 'Could not perform this operation for past date.',
        'statuses' => [
            'requested' => 'Requested',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
        ],
        'props' => [
            'category' => 'Category',
            'requester' => 'Requester',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'status' => 'Status',
            'reason' => 'Reason',
        ],
    ],
    'edit_request' => [
        'edit_request' => 'Edit Request',
        'module_title' => 'List all Edit Requests',
        'module_description' => 'Manage all Edit Requests for Student Information',
        'edit_info' => 'If you want to edit any information, you can submit a request here.',
        'upload_document_info' => 'You can also upload document for proof of your edit request.',
        'submitted' => 'Your request for edit student information is submitted and applied after approval.',
        'already_pending' => 'You have already submitted a request for edit student information.',
        'request_by' => 'Request By',
        'request_by_name' => 'Request By :attribute',
        'already_processed' => 'This request is already processed.',
        'statuses' => [
            'approve' => 'Approve',
            'reject' => 'Reject',
        ],
        'props' => [
            'action' => 'Action',
            'comment' => 'Comment',
            'status' => 'Status',
        ],
    ],
    'have_admission_number' => 'I have an admission number',
    'search_results' => 'Search Results',
    'multiple_matches_found' => 'Multiple students match your search criteria. Please select one from the list.',
    'no_student_found' => 'No student found matching your search criteria.',
    'report' => [
        'report' => 'Report',
        'reports' => 'Reports',
        'batch_wise_attendance' => [
            'batch_wise_attendance' => 'Class Arm Attendance Report',
            'module_title' => 'List all Class Arm Attendance',
            'module_description' => 'Manage all Class Arm Attendance',
        ],
        'subject_wise_attendance' => [
            'subject_wise_attendance' => 'Subject wise Attendance Report',
            'module_title' => 'List all Subject wise Attendance',
            'module_description' => 'Manage all Subject wise Attendance',
        ],
        'date_wise_attendance' => [
            'date_wise_attendance' => 'Date wise Attendance Report',
            'module_title' => 'List all Date wise Attendance',
            'module_description' => 'Manage all Date wise Attendance',
        ],
    ],
];
