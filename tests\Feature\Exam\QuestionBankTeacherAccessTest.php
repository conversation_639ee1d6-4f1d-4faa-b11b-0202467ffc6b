<?php

use App\Models\Academic\Subject;
use App\Models\Employee\Employee;
use App\Models\Exam\QuestionBank;
use App\Models\Incharge;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->seed();
});

it('filters subjects for teachers based on their incharge assignments', function () {
    // Create a teacher user
    $teacher = User::factory()->create();
    $teacher->assignRole('teacher');
    
    // Create an employee record for the teacher
    $employee = Employee::factory()->create([
        'contact_id' => $teacher->contact->id ?? null,
    ]);
    
    // Create subjects
    $subject1 = Subject::factory()->create(['name' => 'Math']);
    $subject2 = Subject::factory()->create(['name' => 'Science']);
    $subject3 = Subject::factory()->create(['name' => 'English']);
    
    // Assign teacher as incharge of Math and Science only
    Incharge::create([
        'employee_id' => $employee->id,
        'model_type' => 'Subject',
        'model_id' => $subject1->id,
        'start_date' => now()->subDays(10),
        'end_date' => null,
    ]);
    
    Incharge::create([
        'employee_id' => $employee->id,
        'model_type' => 'Subject',
        'model_id' => $subject2->id,
        'start_date' => now()->subDays(10),
        'end_date' => null,
    ]);
    
    // Act as the teacher
    $this->actingAs($teacher);
    
    // Test subject filtering
    $filteredSubjects = Subject::query()
        ->byPeriod()
        ->filterTeacherSubjects()
        ->get();
    
    // Should only see Math and Science, not English
    expect($filteredSubjects)->toHaveCount(2);
    expect($filteredSubjects->pluck('name')->toArray())->toContain('Math', 'Science');
    expect($filteredSubjects->pluck('name')->toArray())->not->toContain('English');
});

it('allows admin to see all subjects', function () {
    // Create an admin user
    $admin = User::factory()->create();
    $admin->assignRole('admin');
    
    // Create subjects
    $subject1 = Subject::factory()->create(['name' => 'Math']);
    $subject2 = Subject::factory()->create(['name' => 'Science']);
    $subject3 = Subject::factory()->create(['name' => 'English']);
    
    // Act as the admin
    $this->actingAs($admin);
    
    // Test subject filtering
    $filteredSubjects = Subject::query()
        ->byPeriod()
        ->filterTeacherSubjects()
        ->get();
    
    // Should see all subjects
    expect($filteredSubjects)->toHaveCount(3);
    expect($filteredSubjects->pluck('name')->toArray())->toContain('Math', 'Science', 'English');
});

it('filters question banks for teachers to only show their assigned subjects and created questions', function () {
    // Create teacher and admin users
    $teacher = User::factory()->create();
    $teacher->assignRole('teacher');
    
    $admin = User::factory()->create();
    $admin->assignRole('admin');
    
    // Create employee record for teacher
    $employee = Employee::factory()->create([
        'contact_id' => $teacher->contact->id ?? null,
    ]);
    
    // Create subjects
    $subject1 = Subject::factory()->create(['name' => 'Math']);
    $subject2 = Subject::factory()->create(['name' => 'Science']);
    
    // Assign teacher as incharge of Math only
    Incharge::create([
        'employee_id' => $employee->id,
        'model_type' => 'Subject',
        'model_id' => $subject1->id,
        'start_date' => now()->subDays(10),
        'end_date' => null,
    ]);
    
    // Create question banks
    $mathQuestion = QuestionBank::factory()->create([
        'subject_id' => $subject1->id,
        'user_id' => $admin->id,
        'title' => 'Math Question by Admin',
    ]);
    
    $scienceQuestionByTeacher = QuestionBank::factory()->create([
        'subject_id' => $subject2->id,
        'user_id' => $teacher->id,
        'title' => 'Science Question by Teacher',
    ]);
    
    $scienceQuestionByAdmin = QuestionBank::factory()->create([
        'subject_id' => $subject2->id,
        'user_id' => $admin->id,
        'title' => 'Science Question by Admin',
    ]);
    
    // Act as the teacher
    $this->actingAs($teacher);
    
    // Test question bank filtering
    $filteredQuestions = QuestionBank::query()
        ->filterAccessible()
        ->get();
    
    // Should see:
    // 1. Math question (because teacher is incharge of Math)
    // 2. Science question created by teacher (because teacher created it)
    // Should NOT see:
    // 3. Science question by admin (teacher not incharge of Science and didn't create it)
    expect($filteredQuestions)->toHaveCount(2);
    expect($filteredQuestions->pluck('title')->toArray())->toContain(
        'Math Question by Admin',
        'Science Question by Teacher'
    );
    expect($filteredQuestions->pluck('title')->toArray())->not->toContain(
        'Science Question by Admin'
    );
});
